{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "NODE_ENV=development bun run server/index.ts", "dev:legacy": "NODE_ENV=development tsx server/index.ts", "build": "vite build && bun build server/index.ts --target=node --outdir=dist", "build:legacy": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production bun run dist/index.js", "start:legacy": "NODE_ENV=production node dist/index.js", "check": "tsc", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "bun test", "test:legacy": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:load": "k6 run tests/load-test.js", "db:push": "drizzle-kit push", "db:migrate": "bun run server/migration.ts", "db:migrate:legacy": "tsx server/migration.ts", "db:generate": "drizzle-kit generate", "db:studio": "drizzle-kit studio", "db:optimize": "bun run scripts/optimize-database.ts", "cache:clear": "bun run scripts/clear-cache.ts", "analytics:export": "bun run scripts/export-analytics.ts", "health:check": "bun run scripts/health-check.ts", "recruitment:setup": "bun run populate_recruitment_data.js", "recruitment:setup:legacy": "node populate_recruitment_data.js", "dashboard:protect": "bash scripts/protect_dashboard.sh", "dashboard:status": "bash scripts/protect_dashboard.sh status", "dashboard:backup": "bash scripts/protect_dashboard.sh backup", "dashboard:restore": "bash scripts/protect_dashboard.sh restore", "dashboard:check": "bash scripts/protect_dashboard.sh check", "prepare": "husky install", "install:bun": "bun install", "install:npm": "npm install", "start:all": "concurrently \"npm run dev:legacy\" \"npm run start:enterprise-tms\" \"npm run start:frontend\"", "start:enterprise-tms": "cd enterprise-tms && npm run dev -- --port 3001", "start:frontend": "cd frontend && npm run dev -- --port 3002"}, "dependencies": {"@aws-sdk/client-s3": "^3.535.0", "@aws-sdk/client-sns": "^3.839.0", "@aws-sdk/client-sqs": "^3.839.0", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sentry/integrations": "^7.114.0", "@sentry/node": "^9.28.1", "@sentry/profiling-node": "^9.28.1", "@sentry/react": "^9.32.0", "@sentry/tracing": "^7.120.3", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.60.5", "@tensorflow/tfjs-node": "^4.22.0", "@types/pg": "^8.15.4", "@upstash/redis": "^1.35.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compression": "^1.7.4", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "helmet": "^7.1.0", "input-otp": "^1.4.2", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "mapbox-gl": "^3.12.0", "memorystore": "^1.6.7", "ml-kmeans": "^6.0.0", "ml-matrix": "^6.12.1", "ml-regression": "^6.3.0", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.16.0", "postgres": "^3.4.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "redis": "^4.6.13", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "web-vitals": "^5.0.2", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@playwright/test": "^1.44.0", "@replit/vite-plugin-cartographer": "^0.1.2", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/jsonwebtoken": "^9.0.6", "@types/mapbox-gl": "^3.4.1", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router-dom": "^5.3.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-react": "^4.3.2", "@vitest/coverage-v8": "^1.6.0", "@vitest/ui": "^1.6.0", "autoprefixer": "^10.4.20", "aws-sdk": "^2.1609.0", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "mock-aws-s3": "^4.0.0", "nock": "^13.5.4", "postcss": "^8.4.47", "prettier": "^3.2.5", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14", "vitest": "^1.6.0"}, "optionalDependencies": {"bufferutil": "^4.0.8"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css,scss}": ["prettier --write"]}}