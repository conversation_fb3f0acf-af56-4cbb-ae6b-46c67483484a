/**
 * Upstash Redis Service
 * Cloud Redis replacement for local Redis
 */

import { Redis } from '@upstash/redis';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  nx?: boolean; // Only set if key doesn't exist
  ex?: number;  // Expiration time in seconds
}

export class UpstashRedisService {
  private redis: Redis;
  private isConnected: boolean = false;

  constructor() {
    const url = process.env.UPSTASH_REDIS_REST_URL;
    const token = process.env.UPSTASH_REDIS_REST_TOKEN;

    if (!url || !token) {
      throw new Error('UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN must be set');
    }

    this.redis = new Redis({
      url,
      token,
    });

    this.isConnected = true;
  }

  /**
   * Set a key-value pair
   */
  async set(key: string, value: any, options?: CacheOptions): Promise<boolean> {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      
      if (options?.ttl) {
        await this.redis.setex(key, options.ttl, serializedValue);
      } else if (options?.ex) {
        await this.redis.setex(key, options.ex, serializedValue);
      } else if (options?.nx) {
        const result = await this.redis.setnx(key, serializedValue);
        return result === 1;
      } else {
        await this.redis.set(key, serializedValue);
      }
      
      return true;
    } catch (error) {
      console.error('Redis SET error:', error);
      return false;
    }
  }

  /**
   * Get a value by key
   */
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      
      if (value === null) {
        return null;
      }

      // Try to parse as JSON, fallback to string
      try {
        return JSON.parse(value as string);
      } catch {
        return value as T;
      }
    } catch (error) {
      console.error('Redis GET error:', error);
      return null;
    }
  }

  /**
   * Delete a key
   */
  async del(key: string): Promise<boolean> {
    try {
      const result = await this.redis.del(key);
      return result > 0;
    } catch (error) {
      console.error('Redis DEL error:', error);
      return false;
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis EXISTS error:', error);
      return false;
    }
  }

  /**
   * Set expiration for a key
   */
  async expire(key: string, seconds: number): Promise<boolean> {
    try {
      const result = await this.redis.expire(key, seconds);
      return result === 1;
    } catch (error) {
      console.error('Redis EXPIRE error:', error);
      return false;
    }
  }

  /**
   * Get time to live for a key
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key);
    } catch (error) {
      console.error('Redis TTL error:', error);
      return -1;
    }
  }

  /**
   * Increment a numeric value
   */
  async incr(key: string): Promise<number | null> {
    try {
      return await this.redis.incr(key);
    } catch (error) {
      console.error('Redis INCR error:', error);
      return null;
    }
  }

  /**
   * Increment by a specific amount
   */
  async incrby(key: string, increment: number): Promise<number | null> {
    try {
      return await this.redis.incrby(key, increment);
    } catch (error) {
      console.error('Redis INCRBY error:', error);
      return null;
    }
  }

  /**
   * Decrement a numeric value
   */
  async decr(key: string): Promise<number | null> {
    try {
      return await this.redis.decr(key);
    } catch (error) {
      console.error('Redis DECR error:', error);
      return null;
    }
  }

  /**
   * Add to a list (left push)
   */
  async lpush(key: string, ...values: any[]): Promise<number | null> {
    try {
      const serializedValues = values.map(v => 
        typeof v === 'string' ? v : JSON.stringify(v)
      );
      return await this.redis.lpush(key, ...serializedValues);
    } catch (error) {
      console.error('Redis LPUSH error:', error);
      return null;
    }
  }

  /**
   * Remove from a list (right pop)
   */
  async rpop(key: string): Promise<any | null> {
    try {
      const value = await this.redis.rpop(key);
      if (value === null) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      console.error('Redis RPOP error:', error);
      return null;
    }
  }

  /**
   * Get list length
   */
  async llen(key: string): Promise<number> {
    try {
      return await this.redis.llen(key);
    } catch (error) {
      console.error('Redis LLEN error:', error);
      return 0;
    }
  }

  /**
   * Add to a set
   */
  async sadd(key: string, ...members: any[]): Promise<number | null> {
    try {
      const serializedMembers = members.map(m => 
        typeof m === 'string' ? m : JSON.stringify(m)
      );
      return await this.redis.sadd(key, ...serializedMembers);
    } catch (error) {
      console.error('Redis SADD error:', error);
      return null;
    }
  }

  /**
   * Check if member exists in set
   */
  async sismember(key: string, member: any): Promise<boolean> {
    try {
      const serializedMember = typeof member === 'string' ? member : JSON.stringify(member);
      const result = await this.redis.sismember(key, serializedMember);
      return result === 1;
    } catch (error) {
      console.error('Redis SISMEMBER error:', error);
      return false;
    }
  }

  /**
   * Get all members of a set
   */
  async smembers(key: string): Promise<any[]> {
    try {
      const members = await this.redis.smembers(key);
      return members.map(member => {
        try {
          return JSON.parse(member);
        } catch {
          return member;
        }
      });
    } catch (error) {
      console.error('Redis SMEMBERS error:', error);
      return [];
    }
  }

  /**
   * Set hash field
   */
  async hset(key: string, field: string, value: any): Promise<boolean> {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      const result = await this.redis.hset(key, { [field]: serializedValue });
      return result >= 0;
    } catch (error) {
      console.error('Redis HSET error:', error);
      return false;
    }
  }

  /**
   * Get hash field
   */
  async hget(key: string, field: string): Promise<any | null> {
    try {
      const value = await this.redis.hget(key, field);
      if (value === null) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      console.error('Redis HGET error:', error);
      return null;
    }
  }

  /**
   * Get all hash fields and values
   */
  async hgetall(key: string): Promise<Record<string, any>> {
    try {
      const hash = await this.redis.hgetall(key);
      const result: Record<string, any> = {};
      
      for (const [field, value] of Object.entries(hash)) {
        try {
          result[field] = JSON.parse(value as string);
        } catch {
          result[field] = value;
        }
      }
      
      return result;
    } catch (error) {
      console.error('Redis HGETALL error:', error);
      return {};
    }
  }

  /**
   * Delete hash field
   */
  async hdel(key: string, field: string): Promise<boolean> {
    try {
      const result = await this.redis.hdel(key, field);
      return result > 0;
    } catch (error) {
      console.error('Redis HDEL error:', error);
      return false;
    }
  }

  /**
   * Get keys matching pattern
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.redis.keys(pattern);
    } catch (error) {
      console.error('Redis KEYS error:', error);
      return [];
    }
  }

  /**
   * Flush all data (use with caution)
   */
  async flushall(): Promise<boolean> {
    try {
      await this.redis.flushall();
      return true;
    } catch (error) {
      console.error('Redis FLUSHALL error:', error);
      return false;
    }
  }

  /**
   * Check connection status
   */
  isReady(): boolean {
    return this.isConnected;
  }

  /**
   * Get Redis instance for advanced operations
   */
  getClient(): Redis {
    return this.redis;
  }
}

// Export singleton instance
export const redisService = new UpstashRedisService();

// Export cache helper functions
export const cache = {
  // Session management
  async setSession(sessionId: string, data: any, ttl: number = 3600): Promise<boolean> {
    return redisService.set(`session:${sessionId}`, data, { ttl });
  },

  async getSession(sessionId: string): Promise<any | null> {
    return redisService.get(`session:${sessionId}`);
  },

  async deleteSession(sessionId: string): Promise<boolean> {
    return redisService.del(`session:${sessionId}`);
  },

  // User data caching
  async setUser(userId: string, userData: any, ttl: number = 1800): Promise<boolean> {
    return redisService.set(`user:${userId}`, userData, { ttl });
  },

  async getUser(userId: string): Promise<any | null> {
    return redisService.get(`user:${userId}`);
  },

  // Rate limiting
  async checkRateLimit(key: string, limit: number, window: number): Promise<{ allowed: boolean; remaining: number }> {
    const current = await redisService.incr(`rate:${key}`);
    if (current === 1) {
      await redisService.expire(`rate:${key}`, window);
    }
    
    const remaining = Math.max(0, limit - (current || 0));
    return {
      allowed: (current || 0) <= limit,
      remaining
    };
  },

  // Generic cache operations
  async set(key: string, value: any, ttl?: number): Promise<boolean> {
    return redisService.set(key, value, ttl ? { ttl } : undefined);
  },

  async get<T = any>(key: string): Promise<T | null> {
    return redisService.get<T>(key);
  },

  async del(key: string): Promise<boolean> {
    return redisService.del(key);
  }
};
