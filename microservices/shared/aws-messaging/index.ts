/**
 * AWS SQS/SNS Messaging Service
 * Replaces Kafka with AWS managed services
 */

import { SQSClient, SendMessageCommand, ReceiveMessageCommand, DeleteMessageCommand } from '@aws-sdk/client-sqs';
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';

export interface MessageEvent {
  eventType: string;
  data: any;
  metadata?: {
    timestamp?: string;
    service?: string;
    version?: string;
    [key: string]: any;
  };
}

export interface QueueConfig {
  name: string;
  url: string;
}

export interface TopicConfig {
  name: string;
  arn: string;
}

export class AWSMessagingService {
  private sqsClient: SQSClient;
  private snsClient: SNSClient;
  private queues: Map<string, QueueConfig> = new Map();
  private topics: Map<string, TopicConfig> = new Map();

  constructor(region: string = 'us-east-1') {
    this.sqsClient = new SQSClient({ region });
    this.snsClient = new SNSClient({ region });
    
    // Initialize queue configurations
    this.initializeQueues();
    this.initializeTopics();
  }

  private initializeQueues(): void {
    const accountId = process.env.AWS_ACCOUNT_ID || '************';
    const region = process.env.AWS_REGION || 'us-east-1';
    
    const queueConfigs = [
      'bidbees-tender-events',
      'bidbees-payment-events', 
      'bidbees-task-events',
      'bidbees-user-events',
      'bidbees-notifications',
      'bidbees-supplier-events'
    ];

    queueConfigs.forEach(queueName => {
      this.queues.set(queueName, {
        name: queueName,
        url: `https://sqs.${region}.amazonaws.com/${accountId}/${queueName}`
      });
    });
  }

  private initializeTopics(): void {
    const accountId = process.env.AWS_ACCOUNT_ID || '************';
    const region = process.env.AWS_REGION || 'us-east-1';
    
    this.topics.set('tender-notifications', {
      name: 'bidbees-tender-notifications',
      arn: `arn:aws:sns:${region}:${accountId}:bidbees-tender-notifications`
    });
    
    this.topics.set('system-events', {
      name: 'bidbees-system-events', 
      arn: `arn:aws:sns:${region}:${accountId}:bidbees-system-events`
    });
  }

  /**
   * Send message to SQS queue (replaces Kafka producer)
   */
  async sendMessage(queueName: string, event: MessageEvent): Promise<boolean> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) {
        throw new Error(`Queue ${queueName} not found`);
      }

      const message = {
        ...event,
        metadata: {
          ...event.metadata,
          timestamp: event.metadata?.timestamp || new Date().toISOString(),
          messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        }
      };

      const command = new SendMessageCommand({
        QueueUrl: queue.url,
        MessageBody: JSON.stringify(message),
        MessageAttributes: {
          eventType: {
            DataType: 'String',
            StringValue: event.eventType
          },
          service: {
            DataType: 'String', 
            StringValue: event.metadata?.service || 'unknown'
          }
        }
      });

      await this.sqsClient.send(command);
      console.log(`Message sent to queue ${queueName}:`, event.eventType);
      return true;
    } catch (error) {
      console.error(`Failed to send message to queue ${queueName}:`, error);
      return false;
    }
  }

  /**
   * Receive messages from SQS queue (replaces Kafka consumer)
   */
  async receiveMessages(queueName: string, maxMessages: number = 10): Promise<MessageEvent[]> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) {
        throw new Error(`Queue ${queueName} not found`);
      }

      const command = new ReceiveMessageCommand({
        QueueUrl: queue.url,
        MaxNumberOfMessages: maxMessages,
        WaitTimeSeconds: 20, // Long polling
        MessageAttributeNames: ['All']
      });

      const response = await this.sqsClient.send(command);
      const messages: MessageEvent[] = [];

      if (response.Messages) {
        for (const message of response.Messages) {
          try {
            const parsedMessage = JSON.parse(message.Body || '{}');
            messages.push(parsedMessage);
            
            // Delete message after processing
            await this.deleteMessage(queueName, message.ReceiptHandle!);
          } catch (parseError) {
            console.error('Failed to parse message:', parseError);
          }
        }
      }

      return messages;
    } catch (error) {
      console.error(`Failed to receive messages from queue ${queueName}:`, error);
      return [];
    }
  }

  /**
   * Delete message from SQS queue
   */
  private async deleteMessage(queueName: string, receiptHandle: string): Promise<void> {
    try {
      const queue = this.queues.get(queueName);
      if (!queue) return;

      const command = new DeleteMessageCommand({
        QueueUrl: queue.url,
        ReceiptHandle: receiptHandle
      });

      await this.sqsClient.send(command);
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  }

  /**
   * Publish to SNS topic (for pub/sub messaging)
   */
  async publishToTopic(topicName: string, event: MessageEvent): Promise<boolean> {
    try {
      const topic = this.topics.get(topicName);
      if (!topic) {
        throw new Error(`Topic ${topicName} not found`);
      }

      const message = {
        ...event,
        metadata: {
          ...event.metadata,
          timestamp: event.metadata?.timestamp || new Date().toISOString(),
          messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        }
      };

      const command = new PublishCommand({
        TopicArn: topic.arn,
        Message: JSON.stringify(message),
        MessageAttributes: {
          eventType: {
            DataType: 'String',
            StringValue: event.eventType
          },
          service: {
            DataType: 'String',
            StringValue: event.metadata?.service || 'unknown'
          }
        }
      });

      await this.snsClient.send(command);
      console.log(`Message published to topic ${topicName}:`, event.eventType);
      return true;
    } catch (error) {
      console.error(`Failed to publish to topic ${topicName}:`, error);
      return false;
    }
  }

  /**
   * Get queue configuration
   */
  getQueue(queueName: string): QueueConfig | undefined {
    return this.queues.get(queueName);
  }

  /**
   * Get topic configuration  
   */
  getTopic(topicName: string): TopicConfig | undefined {
    return this.topics.get(topicName);
  }

  /**
   * List all available queues
   */
  listQueues(): string[] {
    return Array.from(this.queues.keys());
  }

  /**
   * List all available topics
   */
  listTopics(): string[] {
    return Array.from(this.topics.keys());
  }
}

// Export singleton instance
export const messagingService = new AWSMessagingService();

// Export event publisher class (replaces Kafka EventPublisher)
export class EventPublisher {
  static async publishTenderCreated(tenderId: string, data: any): Promise<void> {
    await messagingService.sendMessage('bidbees-tender-events', {
      eventType: 'TENDER_CREATED',
      data: { tenderId, ...data },
      metadata: { service: 'tender-service' }
    });
  }

  static async publishTenderUpdated(tenderId: string, data: any): Promise<void> {
    await messagingService.sendMessage('bidbees-tender-events', {
      eventType: 'TENDER_UPDATED', 
      data: { tenderId, ...data },
      metadata: { service: 'tender-service' }
    });
  }

  static async publishPaymentProcessed(paymentId: string, userId: string, amount: number, data?: any): Promise<void> {
    await messagingService.sendMessage('bidbees-payment-events', {
      eventType: 'PAYMENT_PROCESSED',
      data: { paymentId, userId, amount, ...data },
      metadata: { service: 'payment-service' }
    });
  }

  static async publishTaskAssigned(taskId: string, beeId: string, data?: any): Promise<void> {
    await messagingService.sendMessage('bidbees-task-events', {
      eventType: 'TASK_ASSIGNED',
      data: { taskId, beeId, ...data },
      metadata: { service: 'bee-tasks-service' }
    });
  }

  static async publishUserRegistered(userId: string, data: any): Promise<void> {
    await messagingService.sendMessage('bidbees-user-events', {
      eventType: 'USER_REGISTERED',
      data: { userId, ...data },
      metadata: { service: 'user-service' }
    });
  }

  static async publishNotification(userId: string, message: string, data?: any): Promise<void> {
    await messagingService.sendMessage('bidbees-notifications', {
      eventType: 'NOTIFICATION_SENT',
      data: { userId, message, ...data },
      metadata: { service: 'notification-service' }
    });
  }
}
