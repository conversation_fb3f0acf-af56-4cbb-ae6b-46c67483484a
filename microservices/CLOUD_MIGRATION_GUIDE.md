# 🚀 Cloud Migration Guide: <PERSON>fka → AWS SQS/SNS + Local Redis → Upstash

This guide helps you migrate from local infrastructure to cloud services.

## 📋 Migration Summary

### ✅ **Completed Infrastructure Setup:**

**AWS SQS Queues Created:**
- `bidbees-tender-events` - Tender lifecycle events
- `bidbees-payment-events` - Payment processing events  
- `bidbees-task-events` - Task assignment and updates
- `bidbees-user-events` - User registration and activity
- `bidbees-notifications` - Notification delivery
- `bidbees-supplier-events` - Supplier and quote events

**AWS SNS Topics Created:**
- `bidbees-tender-notifications` - Broadcast tender notifications
- `bidbees-system-events` - System-wide event broadcasting

**Dependencies Installed:**
- `@aws-sdk/client-sqs` - AWS SQS client
- `@aws-sdk/client-sns` - AWS SNS client  
- `@upstash/redis` - Upstash Redis client

## 🔧 **Setup Required:**

### 1. **Upstash Redis Setup** (Required)
1. Go to [Upstash Console](https://console.upstash.com/)
2. Sign up/Login with GitHub or Google
3. Create Redis Database:
   - Name: `bidbees-cache`
   - Region: `us-east-1`
   - Type: `Regional` (free tier)
4. Copy connection details to `.env`:
   ```bash
   UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
   UPSTASH_REDIS_REST_TOKEN=your-redis-token
   ```

### 2. **AWS Credentials** (Already configured)
Your AWS credentials are already set up for `us-east-1` region.

## 🔄 **Migration Steps:**

### **Step 1: Update Service Imports**

**Before (Kafka):**
```typescript
import { EventPublisher } from '../config/kafka';
import { initializeKafka } from '../config/kafka';
```

**After (AWS SQS/SNS):**
```typescript
import { EventPublisher, messagingService } from '../../shared/aws-messaging';
import { redisService, cache } from '../../shared/upstash-redis';
```

### **Step 2: Replace Kafka Event Publishing**

**Before:**
```typescript
await EventPublisher.publishTenderCreated(tenderId, data);
```

**After:**
```typescript
await EventPublisher.publishTenderCreated(tenderId, data);
// Same API, different implementation!
```

### **Step 3: Replace Redis Operations**

**Before:**
```typescript
import Redis from 'ioredis';
const redis = new Redis(process.env.REDIS_URL);
await redis.set('key', 'value');
const value = await redis.get('key');
```

**After:**
```typescript
import { cache } from '../../shared/upstash-redis';
await cache.set('key', 'value');
const value = await cache.get('key');
```

### **Step 4: Update Message Consumers**

**Before (Kafka Consumer):**
```typescript
consumer.run({
  eachMessage: async ({ topic, message }) => {
    const data = JSON.parse(message.value.toString());
    await handleMessage(topic, data);
  }
});
```

**After (SQS Consumer):**
```typescript
// Polling-based consumer
setInterval(async () => {
  const messages = await messagingService.receiveMessages('bidbees-tender-events');
  for (const message of messages) {
    await handleMessage('tender-events', message);
  }
}, 5000); // Poll every 5 seconds
```

## 📁 **Service-Specific Migration:**

### **Tender Service:**
```typescript
// Replace: microservices/services/tender-service/src/config/kafka.ts
import { EventPublisher } from '../../../shared/aws-messaging';

// Publishing events
await EventPublisher.publishTenderCreated(tender.id, {
  title: tender.title,
  issuer: tender.issuer,
  value: tender.value
});
```

### **Payment Service:**
```typescript
// Replace: microservices/services/payment-service/src/services/kafkaService.ts
import { EventPublisher } from '../../../shared/aws-messaging';

await EventPublisher.publishPaymentProcessed(
  payment.id, 
  payment.userId, 
  payment.amount
);
```

### **User Service (Redis Caching):**
```typescript
// Replace Redis operations
import { cache } from '../../../shared/upstash-redis';

// Session management
await cache.setSession(sessionId, userData, 3600);
const session = await cache.getSession(sessionId);

// User caching
await cache.setUser(userId, userData, 1800);
const user = await cache.getUser(userId);

// Rate limiting
const { allowed, remaining } = await cache.checkRateLimit(
  `api:${userId}`, 
  100, // 100 requests
  3600 // per hour
);
```

## 🔍 **Testing Migration:**

### **1. Test SQS Messaging:**
```typescript
import { messagingService } from './shared/aws-messaging';

// Send test message
await messagingService.sendMessage('bidbees-tender-events', {
  eventType: 'TEST_EVENT',
  data: { message: 'Hello SQS!' },
  metadata: { service: 'test' }
});

// Receive messages
const messages = await messagingService.receiveMessages('bidbees-tender-events');
console.log('Received messages:', messages);
```

### **2. Test Upstash Redis:**
```typescript
import { cache } from './shared/upstash-redis';

// Test basic operations
await cache.set('test:key', { hello: 'world' }, 60);
const value = await cache.get('test:key');
console.log('Redis test:', value);
```

## 📊 **Benefits Achieved:**

### **Resource Savings:**
- **~2-4 GB RAM** freed up (MongoDB, Redis, Kafka, Zookeeper)
- **~1-2 GB Disk** space freed up
- **Reduced CPU usage** from background processes

### **Scalability Improvements:**
- **Auto-scaling** with AWS managed services
- **Global availability** with Upstash Redis
- **No maintenance** required for infrastructure
- **Built-in monitoring** with AWS CloudWatch

### **Cost Optimization:**
- **Pay-per-use** pricing model
- **Free tiers** available (Upstash Redis)
- **No server maintenance** costs

## 🚨 **Important Notes:**

1. **Gradual Migration:** Migrate one service at a time
2. **Test Thoroughly:** Test each service after migration
3. **Monitor Performance:** Watch AWS CloudWatch metrics
4. **Backup Strategy:** Ensure data backup before migration
5. **Rollback Plan:** Keep local Docker configs for emergency rollback

## 🔗 **Useful Links:**

- [AWS SQS Documentation](https://docs.aws.amazon.com/sqs/)
- [AWS SNS Documentation](https://docs.aws.amazon.com/sns/)
- [Upstash Redis Documentation](https://docs.upstash.com/redis)
- [AWS SDK for JavaScript](https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/)

## 📞 **Support:**

If you encounter issues during migration:
1. Check AWS CloudWatch logs
2. Verify environment variables
3. Test connectivity to Upstash Redis
4. Review AWS IAM permissions

---

**Next Steps:**
1. ✅ Set up Upstash Redis account
2. ✅ Update environment variables  
3. 🔄 Migrate one service at a time
4. 🔄 Test each migration thoroughly
5. 🔄 Monitor performance and costs
